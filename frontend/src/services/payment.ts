import { apiClient } from './api'
import type { CreatePaymentRequest, PaymentResponse } from '@/types/payment'

export class PaymentService {
  /**
   * 获取定价计划
   */
  static async getPricingPlans(): Promise<any[]> {
    try {
      const response = await apiClient.get('/pricing')
      return response.data
    } catch (error: any) {
      console.error('获取定价计划失败:', error)
      throw error
    }
  }
  /**
   * 创建支付记录
   */
  static async createPaymentRecord(membershipTier: string, amount: number): Promise<any> {
    try {
      const response = await apiClient.post('/payment/payments/create', {
        amount: amount,
        currency: 'CNY',
        membership_tier: membershipTier
      })
      return response.data
    } catch (error: any) {
      console.error('创建支付记录失败:', error)
      throw error
    }
  }

  /**
   * 创建Antom支付会话
   */
  static async createAntomSession(paymentId: string): Promise<any> {
    try {
      const response = await apiClient.post('/payment/antom/session', {
        payment_id: paymentId
      })
      return response.data
    } catch (error: any) {
      console.error('创建Antom会话失败:', error)
      throw error
    }
  }

  /**
   * 创建支付（兼容旧接口）
   */
  static async createPayment(planType: 'premium' | 'plus'): Promise<PaymentResponse> {
    try {
      // 根据计划类型确定金额和会员等级
      const planInfo = this.getPlanInfo(planType)

      // 1. 创建支付记录
      const paymentRecord = await this.createPaymentRecord(planInfo.membershipTier, planInfo.amount)

      // 2. 创建Antom会话
      const antomSession = await this.createAntomSession(paymentRecord.payment_id)

      return {
        success: true,
        payment_url: antomSession.redirect_url || antomSession.session_data?.normalUrl
      }
    } catch (error: any) {
      console.error('创建支付失败:', error)
      return {
        success: false,
        error: error.response?.data?.message || '创建支付失败，请稍后重试'
      }
    }
  }

  /**
   * 启动支付流程（使用计划ID）
   */
  static async startPaymentByPlan(planId: string): Promise<void> {
    try {
      // 根据计划ID获取详细信息
      const planDetails = this.getPlanDetails(planId)

      // 1. 创建支付记录
      const paymentRecord = await this.createPaymentRecord(planDetails.membershipTier, planDetails.amount)

      // 2. 创建Antom会话
      const antomSession = await this.createAntomSession(paymentRecord.payment_id)

      // 3. 跳转到支付页面
      const paymentUrl = antomSession.redirect_url || antomSession.session_data?.normalUrl
      if (paymentUrl) {
        window.location.href = paymentUrl
      } else {
        throw new Error('未获取到支付页面URL')
      }
    } catch (error: any) {
      console.error('启动支付失败:', error)
      throw error
    }
  }

  /**
   * 启动支付流程（兼容旧接口）
   */
  static async startPayment(planType: 'premium' | 'plus'): Promise<void> {
    try {
      const result = await this.createPayment(planType)

      if (result.success && result.payment_url) {
        // 跳转到 Antom 支付页面
        window.location.href = result.payment_url
      } else {
        throw new Error(result.error || '创建支付失败')
      }
    } catch (error: any) {
      console.error('启动支付失败:', error)
      throw error
    }
  }

  /**
   * 根据计划ID获取支付类型
   */
  static getPlanType(planId: string): 'premium' | 'plus' {
    // 根据前端的计划ID映射到后端的支付类型
    switch (planId) {
      case 'monthly':
      case 'semi_annual':
        return 'premium'
      case 'annual':
        return 'plus'
      default:
        return 'premium'
    }
  }

  /**
   * 根据计划类型获取支付信息
   */
  static getPlanInfo(planType: 'premium' | 'plus'): { membershipTier: string, amount: number } {
    switch (planType) {
      case 'premium':
        return { membershipTier: 'premium', amount: 39 } // 月付默认金额
      case 'plus':
        return { membershipTier: 'plus', amount: 399 } // 年付金额
      default:
        return { membershipTier: 'premium', amount: 39 }
    }
  }

  /**
   * 根据计划ID获取详细信息
   */
  static getPlanDetails(planId: string): { membershipTier: string, amount: number } {
    switch (planId) {
      case 'monthly':
        return { membershipTier: 'premium', amount: 39 }
      case 'semi_annual':
        return { membershipTier: 'premium', amount: 199 }
      case 'annual':
        return { membershipTier: 'plus', amount: 399 }
      default:
        return { membershipTier: 'premium', amount: 39 }
    }
  }
} 