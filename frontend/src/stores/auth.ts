import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginResponse } from '@/types/auth'
import { apiClient } from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const accessToken = ref<string | null>(localStorage.getItem('access_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refresh_token'))

  const isAuthenticated = computed(() => !!accessToken.value && !!user.value)

  // 设置认证信息
  const setAuth = (data: LoginResponse) => {
    user.value = data.user
    accessToken.value = data.access_token
    refreshToken.value = data.refresh_token

    localStorage.setItem('access_token', data.access_token)
    localStorage.setItem('refresh_token', data.refresh_token)
    
    // 设置API请求头
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${data.access_token}`
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    accessToken.value = null
    refreshToken.value = null

    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    
    delete apiClient.defaults.headers.common['Authorization']
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (accessToken.value) {
      try {
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${accessToken.value}`
        const response = await apiClient.get('/auth/me')
        user.value = response.data.user
      } catch (error) {
        // token无效，清除认证信息
        clearAuth()
      }
    }
  }

  // 刷新token
  const refreshAccessToken = async () => {
    if (!refreshToken.value) {
      clearAuth()
      return false
    }

    try {
      const response = await apiClient.post('/auth/refresh', {
        refresh_token: refreshToken.value
      })
      
      accessToken.value = response.data.access_token
      localStorage.setItem('access_token', response.data.access_token)
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${response.data.access_token}`
      
      return true
    } catch (error) {
      clearAuth()
      return false
    }
  }

  // 登录
  const login = async (phone: string, verificationCode: string): Promise<LoginResponse> => {
    const response = await apiClient.post('/auth/login', {
      phone,
      verification_code: verificationCode
    })
    
    setAuth(response.data)
    return response.data
  }

  // 登出
  const logout = () => {
    clearAuth()
  }

  // 发送验证码
  const sendCode = async (phone: string) => {
    const response = await apiClient.post('/auth/send-code', { phone })
    return response.data
  }

  // 检查用户是否存在
  const checkUserExists = async (phone: string) => {
    const response = await apiClient.post('/auth/check-user', { phone })
    return response.data
  }

  return {
    user,
    accessToken,
    refreshToken,
    isAuthenticated,
    setAuth,
    clearAuth,
    initAuth,
    refreshAccessToken,
    login,
    logout,
    sendCode,
    checkUserExists
  }
}) 