/**
 * The integration method of the Antom CKP.
 */
const amount = { currency: 'USD', value: '6000' };
const PAYMENT_SESSION_URL = 'https://api.z-aiden.com/api/payment/createSession';

// Step 1: Call createPaymentSession endpoint.
/**
 * Fetches payment session data from the backend endpoint.
 * @returns {Object} The response data from the backend if successful; otherwise an error.
 */
async function getPaymentSessionData() {
  try {
    const response = await fetch(PAYMENT_SESSION_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        amountValue: amount.value,
        currency: amount.currency,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const res = await response.json();

    if (res.status === 'success') {
      return res;
    }
    throw new Error(`Invalid response format: ${res.message}`);
  } catch (error) {
    handleError('Error fetching payment session data:', error);
  }
}

// Step 2: Get payment normalUrl from the endpoint.
/**
 * Handles the form submission to get payment normalUrl and render the payment component.
 */
async function handleSubmit() {
  try {
    const { data } = await getPaymentSessionData();
    if (data && data.result.resultStatus === 'S' && data.normalUrl) {
      iframe.src = data.normalUrl;
    } else {
      throw new Error(`Error with payment response: ${JSON.stringify(data)}`);
    }
  } catch (error) {
    handleError('Error submitting the payment request:', error);
  }
}

// Step 3: Initialize the payment processing flow after the document content is fully loaded.
document.addEventListener('DOMContentLoaded', () => {
  initializeIframe();
  handleSubmit();
});

/**
 * Initializes the iframe and sets up the load event listener.
 */
function initializeIframe() {
  const iframe = document.getElementById('iframe');
  iframe.addEventListener('load', () => {
    if (iframe.contentDocument) {
      location.href = iframe.contentWindow.location.href;
    }
  });
}

/**
 * Handles errors by logging them to the console.
 * @param {string} message - The error message prefix.
 * @param {Error|Object} error - The error object or additional error information.
 */
function handleError(message, error) {
  console.error(
    `${message} ${
      error instanceof Error ? error.message : JSON.stringify(error)
    }`
  );
}
